{"success": true, "message": "All trigger logs for user: 7ad86571-5d9a-4a7b-ace0-fbf6946fd540 for duration: 15", "data": [{"startedAt": "2024-01-19T13:56:40.567Z", "expiresAt": "2024-01-19T15:56:40.567Z", "id": "IdkCIo0BumX87aigbqEl", "triggers": [{"mealLogId": "65aa7f9734dd1317cac9c4b8", "mealTags": [{"grade": "B", "tagName": "apple", "nutriScore": 0, "servingSize": "1 cup", "servingQty": 1, "imageUrl": "https://twentydeg-dev.s3.amazonaws.com/users/b12b1963-9931-4c3b-a7d4-8c74b5d71f59/meallogs/cropped/a6e33bc0-8dc4-430e-8bcd-e9227ec98b68.png?...", "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}], "triggeredAt": "2024-01-19T13:56:40.567Z", "graphIconURL": "https://example.com/default_icon.png"}], "activities": [{"eventId": "wNn39IwBumX87aigcqDw", "eventType": "Walking", "timestamp": "2024-01-11T12:29:31.716Z", "imageUrl": "https://twentydeg-dev.s3.amazonaws.com/users/b12b1963-9931-4c3b-a7d4-8c74b5d71f59/meallogs/cropped/a6e33bc0-8dc4-430e-8bcd-e9227ec98b68.png?...", "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "graphIconURL": "https://example.com/default_icon.png"}, {"eventId": "wNn39IwBumX87aigcqDw", "eventType": "sleep", "timestamp": "2024-01-11T12:31:31.716Z", "duration": 23940, "details": {"deep": 12009, "rem": 20019, "awake": 3921, "light": 9920}, "graphIconURL": "https://example.com/default_icon.png"}], "details": {"peakCGM": {"value": 145, "unit": "mg/dl"}, "timeInRange": {"value": 80, "unit": "%"}, "score": {"final": 6.62, "total": 66.21, "breakdown": {"delta": 0, "auc": 54.94, "recovery": 0, "cv": 11.27}}, "glucoseDelta": {"value": 45, "unit": "mg/dl"}, "totalCalories": {"value": 2000, "unit": "kcal"}, "content": {"title": "Daily Summary", "description": "This is a summary of your daily activities and meals."}}}]}