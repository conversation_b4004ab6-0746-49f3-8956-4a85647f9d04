{"type": "object", "required": ["userId", "triggers"], "properties": {"userId": {"type": "string"}, "triggers": {"type": "array", "items": {"type": "object", "properties": {"mealLogId": {"type": "string"}, "triggeredAt": {"type": "string", "format": "date-time"}, "mealTags": {"type": "array"}, "imagePath": {"type": "string"}, "computedNutritions": {"type": "object", "additionalProperties": true}}}}, "metadata": {"type": "array", "items": {"type": "object", "properties": {"eventId": {"type": "string"}, "eventType": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "details": {"type": "object", "additionalProperties": true}}}}, "startedAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}