// ===== CONFIGURABLE PARAMETERS =====
const RECOVERY_THRESHOLD_PERCENT = 10;

const MAX_SPIKE_DELTA = 30;
const MAX_AUC_ABOVE_BASELINE = 6000;
const MAX_RECOVERY_TIME_MIN = 120;
const MAX_CV_PERCENT = 36;

const WEIGHT_DELTA = 30;
const WEIGHT_AUC = 30;
const WEIGHT_RECOVERY = 25;
const WEIGHT_CV = 15;

function getTimeDiffInMinutes(later, earlier) {
  return ((later.getTime() - earlier.getTime()) / 60000).toFixed(2);
}

function calculateBaseline(glucoseData, mealStartTime) {
  const preMealReadings = glucoseData.filter(({ timestamp }) => timestamp < mealStartTime);

  if (preMealReadings.length === 0) return null;

  const total = preMealReadings.reduce((sum, pt) => sum + pt.glucose, 0);
  return (total / preMealReadings.length).toFixed(2);
}

function calculateAUC(glucoseData) {
  let auc = 0;
  for (let i = 1; i < glucoseData.length; i++) {
    const { glucose: g1, timestamp: t1 } = glucoseData[i - 1];
    const { glucose: g2, timestamp: t2 } = glucoseData[i];
    const duration = getTimeDiffInMinutes(t2, t1);
    auc += ((g1 + g2) / 2) * duration;
  }
  return auc.toFixed(2);
}

function calculateRecoveryTime(glucoseData, baseline, mealStartTime) {
  const threshold = baseline * (1 + RECOVERY_THRESHOLD_PERCENT / 100);
  for (let i = glucoseData.length - 1; i >= 0; i--) {
    const reading = glucoseData[i];
    if (reading.glucose <= threshold) {
      const minutesAfterMeal = getTimeDiffInMinutes(reading.timestamp, mealStartTime);
      if (minutesAfterMeal > 0) return minutesAfterMeal;
    }
  }  
  return getTimeDiffInMinutes(glucoseData[glucoseData.length - 1].timestamp, mealStartTime);
}

function calculateCoefficientOfVariation(glucoseData) {
  const values = glucoseData.map(pt => pt.glucose);
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const stdDev = Math.sqrt(values.reduce((sum, val) => sum + (val - mean) ** 2, 0) / values.length);
  return ((stdDev / mean) * 100).toFixed(2);
}

function scoreGlucoseResponse({ delta, aucAboveBaseline, recoveryTime, cv }) {
  const deltaScore = WEIGHT_DELTA * Math.max(0, 1 - delta / MAX_SPIKE_DELTA);
  const aucScore = WEIGHT_AUC * Math.max(0, 1 - aucAboveBaseline / MAX_AUC_ABOVE_BASELINE);
  const recoveryScore = WEIGHT_RECOVERY * Math.max(0, 1 - recoveryTime / MAX_RECOVERY_TIME_MIN);
  const cvScore = WEIGHT_CV * Math.max(0, 1 - cv / MAX_CV_PERCENT);
 
  const totalScore = deltaScore + aucScore + recoveryScore + cvScore;
 
  return {
    final: +(totalScore / 10).toFixed(2),
    total: +totalScore.toFixed(2),
    breakdown: {
      delta: +deltaScore.toFixed(2),
      auc: +aucScore.toFixed(2),
      recovery: +recoveryScore.toFixed(2),
      cv: +cvScore.toFixed(2),
    },
  };
}

function analyzeGlucoseResponse(glucoseData, mealStartTime) {
  if (!glucoseData || glucoseData.length === 0) {
    return {
      final: 0,
      total: 0,
      breakdown: {
        delta: 0,
        auc: 0,
        recovery: 0,
        cv: 0,
      },
    };
  }

  // Convert timestamps to Date objects and sort by timestamp
  const processedData = glucoseData
    .map(entry => ({
      timestamp: new Date(entry.timestamp),
      glucose: entry.value
    }))
    .sort((a, b) => a.timestamp - b.timestamp);

  // 1. baseline
  const baseline = calculateBaseline(processedData, mealStartTime);
  if (!baseline) {
    return {
      final: 0,
      total: 0,
      breakdown: {
        delta: 0,
        auc: 0,
        recovery: 0,
        cv: 0,
      },
    };
  }

  // 2. delta
  const maxGlucose = Math.max(...processedData.map((pt) => pt.glucose));
  const delta = (maxGlucose - baseline).toFixed(2);

  // 3. auc
  const aucTotal = calculateAUC(processedData);
  const totalDuration = getTimeDiffInMinutes(processedData[processedData.length - 1].timestamp, processedData[0].timestamp);
  const aucAboveBaseline = (aucTotal - baseline * totalDuration).toFixed(2);

  // 4. recovery time
  const recoveryTime = calculateRecoveryTime(processedData, baseline, mealStartTime);

  // 5. cv
  const cv = calculateCoefficientOfVariation(processedData);

  const scoreDoc = scoreGlucoseResponse({ delta, aucAboveBaseline, recoveryTime, cv });
  return scoreDoc;
}

function calculateCGMDetails(glucoseData, targetThreshold = 140) {
  if (!glucoseData || glucoseData.length === 0) {
    return {
      peakCGM: 0,
      unit: "mg/dl",
    };
  }

  const processedData = glucoseData
    .map(entry => ({
      timestamp: new Date(entry.timestamp),
      glucose: entry.value
    }))
    .sort((a, b) => a.timestamp - b.timestamp);

  const peakCGM = Math.max(...processedData.map(pt => pt.glucose));

  return {
    peakCGM,
    unit: "mg/dl",
  };
}

function calculateTimeInRange(glucoseData, targetMin = 70, targetMax = 140) {
  if (!glucoseData || glucoseData.length === 0) {
    return 0;
  }

  const processedData = glucoseData
    .map(entry => ({
      timestamp: new Date(entry.timestamp),
      glucose: entry.value
    }))
    .sort((a, b) => a.timestamp - b.timestamp);

  let totalDurationSecs = 0;
  let inRangeDurationSecs = 0;

  for (let i = 1; i < processedData.length; i++) {
    const current = processedData[i];
    const previous = processedData[i - 1];

    const timeDiffMs = current.timestamp.getTime() - previous.timestamp.getTime();
    const timeDiffSecs = Math.round(timeDiffMs / 1000);
    totalDurationSecs += timeDiffSecs;

    // Check if both readings are in range
    if (current.glucose >= targetMin && current.glucose <= targetMax &&
        previous.glucose >= targetMin && previous.glucose <= targetMax) {
      inRangeDurationSecs += timeDiffSecs;
    }
  }

  if (totalDurationSecs === 0) return 0;
  return Math.round((inRangeDurationSecs / totalDurationSecs) * 100);
}

function calculateTotalCaloriesFromMeals(triggers = []) {
  let totalCalories = 0;

  for (const trigger of triggers) {
    if (trigger.mealTags && Array.isArray(trigger.mealTags)) {
      for (const mealTag of trigger.mealTags) {
        if (mealTag.computedNutritions && mealTag.computedNutritions.calories) {
          totalCalories += mealTag.computedNutritions.calories.quantity || 0;
        }
      }
    }
  }

  return totalCalories;
}

function generateCGMDetailsResponse(glucoseData, mealStartTime, options = {}) {
  const {
    targetMin = 70,
    targetMax = 140,
    triggers = [],
    title = "Daily Summary",
    description = "This is a summary of your daily activities and meals."
  } = options;

  // Get existing calculations
  const cgmDetails = calculateCGMDetails(glucoseData, targetMax);
  const scoreData = analyzeGlucoseResponse(glucoseData, mealStartTime);
  const timeInRange = calculateTimeInRange(glucoseData, targetMin, targetMax);

  // Calculate glucose delta (peak - baseline)
  let glucoseDelta = 0;
  if (glucoseData && glucoseData.length > 0) {
    const processedData = glucoseData
      .map(entry => ({
        timestamp: new Date(entry.timestamp),
        glucose: entry.value
      }))
      .sort((a, b) => a.timestamp - b.timestamp);

    const baseline = calculateBaseline(processedData, mealStartTime);
    if (baseline) {
      glucoseDelta = Math.round(cgmDetails.peakCGM - parseFloat(baseline));
    }
  }

  // Calculate total calories from computedNutritions
  const totalCalories = calculateTotalCaloriesFromMeals(triggers);

  return {
    details: {
      peakCGM: {
        value: cgmDetails.peakCGM,
        unit: "mg/dl"
      },
      timeInRange: {
        value: timeInRange,
        unit: "%"
      },
      score: scoreData,
      glucoseDelta: {
        value: glucoseDelta,
        unit: "mg/dl"
      },
      totalCalories: {
        value: totalCalories,
        unit: "kcal"
      },
      content: {
        title,
        description
      }
    }
  };
}

module.exports = {
  analyzeGlucoseResponse,
  calculateCGMDetails,
  calculateBaseline,
  calculateAUC,
  calculateRecoveryTime,
  calculateCoefficientOfVariation,
  scoreGlucoseResponse,
  calculateTimeInRange,
  generateCGMDetailsResponse
};
